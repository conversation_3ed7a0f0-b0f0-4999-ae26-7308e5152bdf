import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/ios_simulator_detector.dart';

/// 照片选择高级诊断工具
/// 专门诊断"能打开相册但无法选中照片"的问题
class PhotoSelectionAdvancedDiagnostics extends StatefulWidget {
  const PhotoSelectionAdvancedDiagnostics({super.key});

  @override
  State<PhotoSelectionAdvancedDiagnostics> createState() => _PhotoSelectionAdvancedDiagnosticsState();
}

class _PhotoSelectionAdvancedDiagnosticsState extends State<PhotoSelectionAdvancedDiagnostics> {
  final ImagePicker _imagePicker = ImagePicker();
  final List<String> _diagnosticLogs = [];
  
  bool _isRunningDiagnostics = false;
  bool? _isSimulator;
  Map<String, dynamic>? _deviceInfo;
  Map<String, dynamic>? _permissionAnalysis;
  Map<String, dynamic>? _imagePickerAnalysis;
  
  // 测试结果
  bool? _canOpenGallery;
  bool? _canSelectSingle;
  bool? _canSelectMultiple;
  bool? _canReturnToApp;
  List<String> _detectedIssues = [];

  @override
  void initState() {
    super.initState();
    _initializeDiagnostics();
  }

  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _diagnosticLogs.add('[$timestamp] $message');
    });
    print(message);
  }

  /// 初始化诊断工具
  Future<void> _initializeDiagnostics() async {
    _addLog('🔬 高级诊断工具已启动');
    _addLog('🎯 专门诊断：能打开相册但无法选中照片的问题');
    
    try {
      // 检测环境
      _isSimulator = await IOSSimulatorDetector.isSimulator();
      _addLog('📱 环境检测: ${_isSimulator! ? "iOS模拟器" : "真机"}');
      
      if (_isSimulator!) {
        _deviceInfo = await IOSSimulatorDetector.getDebugInfo();
        _addLog('📋 模拟器信息已获取');
      }
      
      _addLog('✅ 诊断工具初始化完成');
    } catch (e) {
      _addLog('❌ 初始化失败: $e');
    }
  }

  /// 运行完整诊断流程
  Future<void> _runComprehensiveDiagnostics() async {
    _addLog('🚀 开始运行完整诊断流程...');
    
    setState(() {
      _isRunningDiagnostics = true;
      _detectedIssues.clear();
    });
    
    try {
      // 第一步：权限深度分析
      await _analyzePermissions();
      
      // 第二步：ImagePicker配置分析
      await _analyzeImagePickerConfiguration();
      
      // 第三步：模拟器环境分析
      if (_isSimulator!) {
        await _analyzeSimulatorEnvironment();
      }
      
      // 第四步：逐步功能测试
      await _performStepByStepTesting();
      
      // 第五步：生成诊断报告
      await _generateDiagnosticReport();
      
    } catch (e) {
      _addLog('❌ 诊断流程异常: $e');
    } finally {
      setState(() {
        _isRunningDiagnostics = false;
      });
    }
  }

  /// 权限深度分析
  Future<void> _analyzePermissions() async {
    _addLog('🔐 开始权限深度分析...');
    
    try {
      final analysis = <String, dynamic>{};
      
      // 检查Photos权限
      final photosStatus = await Permission.photos.status;
      analysis['photos'] = photosStatus.toString();
      _addLog('📸 Photos权限: $photosStatus');
      
      // 检查PhotosAddOnly权限（iOS特有）
      if (Platform.isIOS) {
        final photosAddOnlyStatus = await Permission.photosAddOnly.status;
        analysis['photosAddOnly'] = photosAddOnlyStatus.toString();
        _addLog('📸 PhotosAddOnly权限: $photosAddOnlyStatus');
      }
      
      // 权限历史检查
      if (photosStatus.isDenied) {
        _addLog('⚠️ 权限被拒绝 - 这可能是主要问题');
        _detectedIssues.add('照片权限被拒绝');
      } else if (photosStatus.isPermanentlyDenied) {
        _addLog('❌ 权限被永久拒绝 - 需要手动设置');
        _detectedIssues.add('照片权限被永久拒绝');
      } else if (photosStatus.isGranted) {
        _addLog('✅ 权限已授予');
      } else if (photosStatus.isRestricted) {
        _addLog('🔒 权限受限制 - 可能是家长控制');
        _detectedIssues.add('照片权限受限制');
      }
      
      setState(() {
        _permissionAnalysis = analysis;
      });
      
    } catch (e) {
      _addLog('❌ 权限分析失败: $e');
    }
  }

  /// ImagePicker配置分析
  Future<void> _analyzeImagePickerConfiguration() async {
    _addLog('⚙️ 开始ImagePicker配置分析...');
    
    try {
      final analysis = <String, dynamic>{};
      
      // 检查ImagePicker实例
      _addLog('📦 ImagePicker版本检查...');
      analysis['instance_created'] = true;
      
      // 检查支持的功能
      _addLog('🔍 检查支持的功能...');
      
      // 模拟器特殊配置检查
      if (_isSimulator!) {
        _addLog('📱 模拟器特殊配置检查...');
        analysis['simulator_optimized'] = true;
        
        // 检查模拟器是否支持照片选择
        final strategy = await IOSSimulatorDetector.getSimulatorPermissionStrategy();
        analysis['simulator_strategy'] = strategy;
        _addLog('🔧 模拟器策略: $strategy');
      }
      
      setState(() {
        _imagePickerAnalysis = analysis;
      });
      
    } catch (e) {
      _addLog('❌ ImagePicker配置分析失败: $e');
    }
  }

  /// 模拟器环境分析
  Future<void> _analyzeSimulatorEnvironment() async {
    _addLog('🖥️ 开始模拟器环境分析...');
    
    try {
      // 检查模拟器版本
      _addLog('📱 模拟器版本信息:');
      if (_deviceInfo != null) {
        _deviceInfo!.forEach((key, value) {
          _addLog('  - $key: $value');
        });
      }
      
      // 检查模拟器照片库状态
      final hasPhotos = await IOSSimulatorDetector.hasPhotosInSimulator();
      _addLog('📷 模拟器照片库: ${hasPhotos ? "有照片" : "可能为空"}');
      
      if (!hasPhotos) {
        _detectedIssues.add('模拟器相册可能为空');
      }
      
      // 检查模拟器特殊限制
      _addLog('🔍 检查模拟器特殊限制...');
      
      // iOS模拟器的已知问题
      _addLog('⚠️ iOS模拟器已知问题检查:');
      _addLog('  - 某些iOS版本的模拟器存在照片选择bug');
      _addLog('  - 高分辨率照片可能导致选择失败');
      _addLog('  - 内存压力可能影响照片选择');
      
    } catch (e) {
      _addLog('❌ 模拟器环境分析失败: $e');
    }
  }

  /// 逐步功能测试
  Future<void> _performStepByStepTesting() async {
    _addLog('🧪 开始逐步功能测试...');
    
    // 测试1：单张照片选择
    await _testSinglePhotoSelection();
    
    // 测试2：多张照片选择
    await _testMultiplePhotoSelection();
    
    // 测试3：超时测试
    await _testSelectionTimeout();
  }

  /// 测试单张照片选择
  Future<void> _testSinglePhotoSelection() async {
    _addLog('📸 测试单张照片选择...');
    
    try {
      final completer = Completer<bool>();
      bool testCompleted = false;
      
      // 设置超时
      Timer(const Duration(seconds: 30), () {
        if (!testCompleted) {
          testCompleted = true;
          completer.complete(false);
          _addLog('⏰ 单张照片选择超时');
        }
      });
      
      _addLog('📱 调用pickImage...');
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );
      
      if (!testCompleted) {
        testCompleted = true;
        if (image != null) {
          _addLog('✅ 单张照片选择成功: ${image.path}');
          setState(() {
            _canSelectSingle = true;
          });
          completer.complete(true);
        } else {
          _addLog('⚠️ 单张照片选择返回null（用户取消或失败）');
          setState(() {
            _canSelectSingle = false;
          });
          _detectedIssues.add('单张照片选择失败');
          completer.complete(false);
        }
      }
      
      await completer.future;
      
    } catch (e) {
      _addLog('❌ 单张照片选择异常: $e');
      setState(() {
        _canSelectSingle = false;
      });
      _detectedIssues.add('单张照片选择异常: $e');
    }
  }

  /// 测试多张照片选择
  Future<void> _testMultiplePhotoSelection() async {
    _addLog('📸 测试多张照片选择...');
    
    try {
      final completer = Completer<bool>();
      bool testCompleted = false;
      
      // 设置超时
      Timer(const Duration(seconds: 30), () {
        if (!testCompleted) {
          testCompleted = true;
          completer.complete(false);
          _addLog('⏰ 多张照片选择超时');
        }
      });
      
      _addLog('📱 调用pickMultiImage...');
      final List<XFile> images = await _imagePicker.pickMultiImage(
        imageQuality: 80,
      );
      
      if (!testCompleted) {
        testCompleted = true;
        if (images.isNotEmpty) {
          _addLog('✅ 多张照片选择成功: ${images.length} 张');
          setState(() {
            _canSelectMultiple = true;
          });
          completer.complete(true);
        } else {
          _addLog('⚠️ 多张照片选择返回空列表');
          setState(() {
            _canSelectMultiple = false;
          });
          _detectedIssues.add('多张照片选择失败');
          completer.complete(false);
        }
      }
      
      await completer.future;
      
    } catch (e) {
      _addLog('❌ 多张照片选择异常: $e');
      setState(() {
        _canSelectMultiple = false;
      });
      _detectedIssues.add('多张照片选择异常: $e');
    }
  }

  /// 测试选择超时
  Future<void> _testSelectionTimeout() async {
    _addLog('⏱️ 测试选择超时机制...');
    
    // 这个测试主要是检查是否存在无限等待的情况
    _addLog('💡 如果前面的测试都超时了，说明存在严重的配置问题');
    
    if (_canSelectSingle == false && _canSelectMultiple == false) {
      _detectedIssues.add('所有照片选择功能都超时或失败');
      _addLog('❌ 检测到严重问题：所有照片选择功能都无法正常工作');
    }
  }

  /// 生成诊断报告
  Future<void> _generateDiagnosticReport() async {
    _addLog('📊 生成诊断报告...');
    
    _addLog('');
    _addLog('=== 诊断报告 ===');
    _addLog('');
    
    // 环境信息
    _addLog('📱 环境信息:');
    _addLog('  - 设备类型: ${_isSimulator! ? "iOS模拟器" : "真机"}');
    if (_deviceInfo != null) {
      _addLog('  - 模拟器型号: ${_deviceInfo!['simulatorModel'] ?? "未知"}');
    }
    
    // 权限状态
    _addLog('');
    _addLog('🔐 权限状态:');
    if (_permissionAnalysis != null) {
      _permissionAnalysis!.forEach((key, value) {
        _addLog('  - $key: $value');
      });
    }
    
    // 功能测试结果
    _addLog('');
    _addLog('🧪 功能测试结果:');
    _addLog('  - 单张照片选择: ${_canSelectSingle == true ? "✅ 成功" : "❌ 失败"}');
    _addLog('  - 多张照片选择: ${_canSelectMultiple == true ? "✅ 成功" : "❌ 失败"}');
    
    // 发现的问题
    _addLog('');
    _addLog('⚠️ 发现的问题:');
    if (_detectedIssues.isEmpty) {
      _addLog('  - 无明显问题');
    } else {
      for (final issue in _detectedIssues) {
        _addLog('  - $issue');
      }
    }
    
    // 解决建议
    _addLog('');
    _addLog('💡 解决建议:');
    await _generateSolutions();
  }

  /// 生成解决方案
  Future<void> _generateSolutions() async {
    if (_detectedIssues.contains('照片权限被拒绝') || _detectedIssues.contains('照片权限被永久拒绝')) {
      _addLog('  1. 重置照片权限：');
      _addLog('     - 设置 → 隐私与安全性 → 照片 → OneDay → 所有照片');
      _addLog('     - 或使用命令：xcrun simctl privacy booted reset photos');
    }
    
    if (_detectedIssues.contains('模拟器相册可能为空')) {
      _addLog('  2. 添加测试照片：');
      _addLog('     - 从Mac拖拽照片到模拟器');
      _addLog('     - 或在Safari中保存网络图片');
    }
    
    if (_detectedIssues.contains('所有照片选择功能都超时或失败')) {
      _addLog('  3. 系统级解决方案：');
      _addLog('     - 重启模拟器');
      _addLog('     - 重置模拟器内容和设置');
      _addLog('     - 更新Xcode和模拟器');
      _addLog('     - 尝试不同的模拟器版本');
    }
    
    if (_isSimulator!) {
      _addLog('  4. 模拟器特殊建议：');
      _addLog('     - 确保模拟器有足够内存');
      _addLog('     - 关闭其他占用资源的应用');
      _addLog('     - 尝试使用较低分辨率的照片');
    }
  }

  /// 清空日志
  void _clearLogs() {
    setState(() {
      _diagnosticLogs.clear();
      _detectedIssues.clear();
      _canSelectSingle = null;
      _canSelectMultiple = null;
      _permissionAnalysis = null;
      _imagePickerAnalysis = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('照片选择高级诊断'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearLogs,
            icon: const Icon(Icons.clear_all),
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 控制面板
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton.icon(
                    onPressed: _isRunningDiagnostics ? null : _runComprehensiveDiagnostics,
                    icon: _isRunningDiagnostics 
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.medical_services),
                    label: Text(_isRunningDiagnostics ? '诊断中...' : '运行完整诊断'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                
                // 状态指示器
                if (_canSelectSingle != null || _canSelectMultiple != null) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _canSelectSingle == true ? Colors.green : Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '单张选择: ${_canSelectSingle == true ? "成功" : "失败"}',
                            style: const TextStyle(color: Colors.white, fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _canSelectMultiple == true ? Colors.green : Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '多张选择: ${_canSelectMultiple == true ? "成功" : "失败"}',
                            style: const TextStyle(color: Colors.white, fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          
          // 日志显示区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: ListView.builder(
                itemCount: _diagnosticLogs.length,
                itemBuilder: (context, index) {
                  final log = _diagnosticLogs[index];
                  Color textColor = Colors.white;
                  
                  if (log.contains('❌')) {
                    textColor = Colors.red;
                  } else if (log.contains('⚠️')) {
                    textColor = Colors.orange;
                  } else if (log.contains('✅')) {
                    textColor = Colors.green;
                  } else if (log.contains('🔬') || log.contains('🧪')) {
                    textColor = Colors.cyan;
                  } else if (log.contains('💡')) {
                    textColor = Colors.yellow;
                  } else if (log.contains('===')) {
                    textColor = Colors.lightBlue;
                  }
                  
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 1),
                    child: Text(
                      log,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 11,
                        fontFamily: 'monospace',
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
